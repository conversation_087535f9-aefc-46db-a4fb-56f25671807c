# Assistente Virtuale per Documentazione di Prodotto

Questo progetto implementa un chatbot basato su AI in grado di rispondere a domande degli utenti basandosi su una serie di documenti PDF specifici per un prodotto. Utilizza Jina AI per la generazione di embeddings, Google Gemini per la creazione di risposte conversazionali e ChromaDB per l'archiviazione e la ricerca vettoriale.

L'applicazione offre due interfacce utente: una chat a pagina intera e un widget di chat "embedded" progettato per essere facilmente integrato in siti web di terze parti.

## Indice

- [Caratteristiche Principali](#caratteristiche-principali)
- [Come Funziona](#come-funziona)
- [Struttura del Progetto](#struttura-del-progetto)
- [Installazione e Configurazione](#installazione-e-configurazione)
- [Utilizzo](#utilizzo)
- [Integrazione del Widget Embedded](#integrazione-del-widget-embedded)
- [Configurazione Avanzata](#configurazione-avanzata)
- [Endpoint API](#endpoint-api)

## Caratteristiche Principali

- **Elaborazione Dinamica dei PDF**: Indicizza automaticamente i documenti PDF associati a un codice prodotto.
- **Ricerca Semantica**: Utilizza embeddings vettoriali per trovare le informazioni più pertinenti all'interno dei documenti.
- **Risposte Contestuali**: Sfrutta la cronologia della conversazione per interpretare meglio l'intento dell'utente e fornire risposte più accurate.
- **Verbosità Configurabile**: Permette di impostare il livello di dettaglio delle risposte del chatbot (da 1 a 5).
- **Riferimenti ai Documenti**: Le risposte includono link diretti alle pagine dei PDF da cui sono state estratte le informazioni.
- **Doppia Interfaccia**: Offre sia una versione a pagina intera che un widget "embedded" per la massima flessibilità.
- **Caching Intelligente**: Rielabora solo i file PDF che sono stati modificati, per ottimizzare i tempi di avvio.

## Come Funziona

L'architettura del sistema si basa su un backend Python che utilizza il micro-framework Flask e un frontend HTML, CSS e JavaScript.

1.  **Backend (Python/Flask)**:
    -   `app.py`: Il file principale che gestisce le rotte web, le richieste API e l'inizializzazione del chatbot.
    -   `pdf_chatbot_prodotti.py`: Contiene la logica di base del chatbot (`ProductPDFChatbot`). Questa classe si occupa di:
        -   **Elaborazione dei PDF**: Estrarre il testo dai file PDF.
        -   **Creazione di Embeddings**: Convertire i chunk di testo in vettori numerici utilizzando l'API di Jina.
        -   **Indicizzazione**: Salvare i vettori e i metadati associati in un database ChromaDB.
        -   **Ricerca**: Eseguire ricerche di similarità per trovare i chunk di testo più rilevanti per una data domanda.
        -   **Generazione Risposta**: Inviare la domanda, la cronologia della chat e il contesto recuperato al modello Gemini per generare una risposta in linguaggio naturale.

2.  **Frontend (HTML/CSS/JS)**:
    -   `templates/index.html`: Template per la chat a pagina intera.
    -   `templates/embedded.html`: Template per il widget di chat.
    -   `static/`: Contiene i file CSS e JavaScript che gestiscono lo stile e l'interattività delle interfacce.

## Struttura del Progetto

```
/
├── app.py                  # Applicazione principale Flask
├── pdf_chatbot_prodotti.py # Logica del chatbot
├── requirements.txt        # Dipendenze Python
├── .env                    # File per le variabili d'ambiente (da creare)
├── .gitignore              # File ignorati da Git
├── README.md               # Questo file
├── pdf/                    # Cartella contenente i PDF dei prodotti
│   └── CODICE_PRODOTTO_A/
│       └── manuale.pdf
├── chromadb_data/          # Database vettoriale di ChromaDB
├── guardrails_py/          # Moduli di sicurezza per il chatbot
│   ├── __init__.py
│   ├── guardrail_manager.py
│   ├── input/
│   │   ├── __init__.py
│   │   ├── input_length_control_guardrail.py
│   │   ├── language_detection_guardrail.py
│   │   └── pii_filter_guardrail.py
│   └── output/
│       ├── __init__.py
│       ├── harmful_content_filter_guardrail.py
│       └── bias_fairness_mitigation_guardrail.py
├── static/                 # File statici (CSS, JS)
│   ├── css/
│   │   ├── style.css
│   │   └── embedded.css
│   └── js/
│       ├── script.js
│       └── embedded.js
└── templates/              # Template HTML di Flask
    ├── index.html
    └── embedded.html
```

## Installazione e Configurazione

Segui questi passaggi per eseguire il progetto in locale.

**Prerequisiti**:
- Python 3.8+
- `pip` (gestore di pacchetti Python)

**1. Clona il Repository**
```bash
git clone <URL_DEL_TUO_REPOSITORY>
cd <NOME_DELLA_CARTELLA>
```

**2. Crea un Ambiente Virtuale**
È consigliabile utilizzare un ambiente virtuale per isolare le dipendenze del progetto.
```bash
python -m venv env
source env/bin/activate  # Su Windows: env\Scripts\activate
```

**3. Installa le Dipendenze**
```bash
pip install -r requirements.txt
```

**4. Configura le Variabili d'Ambiente**
Crea un file chiamato `.env` nella root del progetto e aggiungi le tue API key e le configurazioni desiderate.

```dotenv
# .env
JINA_API_KEY="tua_api_key_jina"
GEMINI_API_KEY="tua_api_key_gemini"
VERBOSITY_LEVEL=3
```

- `JINA_API_KEY`: La tua chiave API per Jina AI.
- `GEMINI_API_KEY`: La tua chiave API per Google Gemini.
- `VERBOSITY_LEVEL`: Il livello di dettaglio delle risposte (da 1 a 5).

**5. Aggiungi i Documenti PDF**
Crea una sottocartella all'interno della cartella `pdf/` per ogni codice prodotto. Il nome della sottocartella deve corrispondere esattamente al codice prodotto che userai nell'interfaccia.

Esempio:
- `pdf/CODICE_A/manuale1.pdf`
- `pdf/CODICE_A/manuale2.pdf`
- `pdf/CODICE_B/specifiche.pdf`

## Utilizzo

Una volta completata la configurazione, avvia l'applicazione Flask:

```bash
python app.py
```

L'applicazione sarà accessibile ai seguenti indirizzi:
- **Chat a Pagina Intera**: `http://127.0.0.1:5000/`
- **Widget Embedded**: `http://127.0.0.1:5000/embedded`

Al primo avvio per un nuovo codice prodotto, il sistema richiederà del tempo per elaborare e indicizzare i documenti PDF. Le esecuzioni successive saranno molto più veloci.

## Integrazione del Widget Embedded

Per integrare il chatbot in una qualsiasi pagina web esterna, aggiungi il seguente codice HTML. Puoi personalizzare le dimensioni (`width` e `height`) secondo le tue necessità.

```html
<iframe 
    src="http://127.0.0.1:5000/embedded" 
    width="400" 
    height="600" 
    style="border:none; position:fixed; bottom:20px; right:20px; z-index: 9999;"
    title="Assistente Virtuale">
</iframe>
```

Questo `<iframe>` mostrerà il pulsante di avvio del chatbot nell'angolo in basso a destra della pagina ospitante.

## Guardrails

Il sistema integra una serie di "guardrails" per garantire la sicurezza, l'affidabilità e l'eticità delle interazioni del chatbot. Questi moduli, implementati in Python e situati nella cartella `guardrails_py`, analizzano sia l'input dell'utente che l'output del modello AI per prevenire abusi e contenuti inappropriati.

### Guardrails di Input

Questi guardrail processano la richiesta dell'utente prima che venga inviata al modello AI.

- **Input Length Control**: Controlla la lunghezza dell'input per prevenire attacchi di tipo Denial of Service (DoS) e ottimizzare le performance. Respinge o tronca i messaggi eccessivamente lunghi.
- **Language Detection**: Rileva la lingua dell'input per assicurarsi che sia tra quelle supportate e applica policy specifiche per ogni lingua.
- **PII Filter**: Utilizza la libreria `presidio` per identificare e anonimizzare informazioni personali sensibili (es. email, numeri di telefono, codici fiscali) per proteggere la privacy dell'utente e garantire la conformità al GDPR.
- **Prompt Injection**: Rileva e blocca i tentativi di "prompt injection", in cui un utente malintenzionato cerca di manipolare il comportamento del modello AI con istruzioni nascoste.

### Guardrails di Output

Questi guardrail analizzano la risposta generata dal modello AI prima che venga mostrata all'utente.

- **Harmful Content Filter**: Filtra contenuti dannosi, offensivi o inappropriati (es. violenza, incitamento all'odio, disinformazione). Se rileva un problema, sostituisce la risposta con un messaggio sicuro.
- **Bias & Fairness Mitigation**: Analizza l'output per identificare e correggere bias legati a genere, etnia o età, promuovendo un linguaggio equo e inclusivo.

## Configurazione Avanzata

### Livelli di Verbosità

Puoi modificare il comportamento del chatbot cambiando il valore di `VERBOSITY_LEVEL` nel file `.env`:

-   **1 (Conciso)**: Risposte brevi e dirette.
-   **2 (Poco Dettagliato)**: Risposte con brevi spiegazioni.
-   **3 (Bilanciato)**: Il giusto equilibrio tra dettaglio e concisione (default).
-   **4 (Articolato)**: Risposte complete che approfondiscono l'argomento.
-   **5 (Esaustivo)**: Spiegazioni approfondite con esempi e contesto aggiuntivo.

## Endpoint API

L'applicazione espone alcuni endpoint API per la gestione della chat:

-   `POST /prepare`
    -   Prepara i documenti per un dato codice prodotto.
    -   **Payload**: `{"product_code": "CODICE_X"}`
    -   **Risposta**: `{"success": true/false, "message": "..."}`

-   `POST /chat`
    -   Invia un messaggio al chatbot e riceve una risposta.
    -   **Payload**: `{"message": "...", "product_code": "CODICE_X", "history": [...]}`
    -   **Risposta**: `{"answer": "...", "history": [...]}`
