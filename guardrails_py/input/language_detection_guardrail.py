
import re
from collections import Counter

class LanguageDetectionGuardrail:
    def __init__(self, config={}):
        self.config = {
            "supported_languages": ["it", "en"],
            "default_language": "it",
            "confidence_threshold": 0.6,
            **config
        }
        self._initialize_language_patterns()

    def _initialize_language_patterns(self):
        """Initialize language detection patterns for Italian and English"""
        self.language_patterns = {
            "it": {
                # Common Italian words and patterns
                "common_words": [
                    r"\b(il|la|lo|gli|le|di|da|in|con|su|per|tra|fra|a|e|o|ma|se|che|chi|come|quando|dove|perché|però|anche|già|più|molto|tutto|tutti|ogni|alcuni|qualche|questo|questa|quello|quella|essere|avere|fare|dire|andare|venire|stare|dare|sapere|vedere|dovere|potere|volere|bene|male|grande|piccolo|nuovo|vecchio|primo|ultimo|altro|stesso|proprio|nostro|vostro|loro|mio|tuo|suo)\b",
                ],
                "endings": [
                    r"\w+(zione|sione|mente|ezza|ità|aggio|ismo|ista|ore|ice|oso|osa|ivo|iva|ato|ata|uto|uta|ito|ita)\b",
                ],
                "articles": [r"\b(il|la|lo|gli|le|un|una|uno)\b"],
                "prepositions": [r"\b(di|da|in|con|su|per|tra|fra|a)\b"]
            },
            "en": {
                # Common English words and patterns
                "common_words": [
                    r"\b(the|be|to|of|and|a|in|that|have|i|it|for|not|on|with|he|as|you|do|at|this|but|his|by|from|they|she|or|an|will|my|one|all|would|there|their|what|so|up|out|if|about|who|get|which|go|me|when|make|can|like|time|no|just|him|know|take|people|into|year|your|good|some|could|them|see|other|than|then|now|look|only|come|its|over|think|also|back|after|use|two|how|our|work|first|well|way|even|new|want|because|any|these|give|day|most|us)\b",
                ],
                "endings": [
                    r"\w+(ing|ed|er|est|ly|tion|sion|ness|ment|ful|less|able|ible)\b",
                ],
                "articles": [r"\b(the|a|an)\b"],
                "prepositions": [r"\b(in|on|at|by|for|with|from|to|of|about|into|through|during|before|after|above|below|up|down|out|off|over|under|again|further|then|once)\b"]
            }
        }

    def process(self, text, context={}):
        try:
            if len(text.strip()) < 10:  # Too short to reliably detect
                return {
                    "action": "ALLOW",
                    "details": {"detected_language": self.config["default_language"], "confidence": 0.5, "reason": "text_too_short"}
                }

            language, confidence = self._detect_language(text)

            if (
                language not in self.config["supported_languages"] or
                confidence < self.config["confidence_threshold"]
            ):
                return {
                    "action": "BLOCK",
                    "reason": "Unsupported or uncertain language",
                    "details": {"detected_language": language, "confidence": confidence}
                }

            return {
                "action": "ALLOW",
                "details": {"detected_language": language, "confidence": confidence}
            }
        except Exception as e:
            return {
                "action": "BLOCK",
                "reason": f"Language detection failed: {str(e)}",
                "details": {}
            }

    def _detect_language(self, text):
        """Simple regex-based language detection for Italian and English"""
        text_lower = text.lower()
        scores = {}

        for lang, patterns in self.language_patterns.items():
            score = 0
            total_matches = 0

            # Count matches for each pattern type
            for pattern_type, pattern_list in patterns.items():
                for pattern in pattern_list:
                    matches = len(re.findall(pattern, text_lower, re.IGNORECASE))
                    total_matches += matches

                    # Weight different pattern types
                    if pattern_type == "common_words":
                        score += matches * 3
                    elif pattern_type == "articles":
                        score += matches * 2
                    elif pattern_type == "prepositions":
                        score += matches * 2
                    else:
                        score += matches

            scores[lang] = score

        # Determine the most likely language
        if not scores or max(scores.values()) == 0:
            return self.config["default_language"], 0.3

        best_lang = max(scores, key=scores.get)
        total_score = sum(scores.values())
        confidence = scores[best_lang] / total_score if total_score > 0 else 0.3

        # Ensure confidence is between 0 and 1
        confidence = min(1.0, max(0.0, confidence))

        return best_lang, confidence

