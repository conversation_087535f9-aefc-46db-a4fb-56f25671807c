
from langdetect import detect, DetectorFactory, detect_langs
from langdetect.lang_detect_exception import LangDetectException

# Ensure consistent detection results
DetectorFactory.seed = 0

class LanguageDetectionGuardrail:
    def __init__(self, config={}):
        self.config = {
            "supported_languages": ["it", "en", "fr", "es", "de"],
            "default_language": "it",
            "confidence_threshold": 0.7,
            **config
        }

    def process(self, text, context={}):
        try:
            language = detect(text)
            confidence = self._get_confidence(text, language)

            if (
                language not in self.config["supported_languages"] or
                confidence < self.config["confidence_threshold"]
            ):
                return {
                    "action": "BLOCK",
                    "reason": "Unsupported or uncertain language",
                    "details": {"detected_language": language, "confidence": confidence}
                }

            return {
                "action": "ALLOW",
                "details": {"detected_language": language, "confidence": confidence}
            }
        except LangDetectException:
            return {
                "action": "BLOCK",
                "reason": "Language detection failed",
                "details": {}
            }

    def _get_confidence(self, text, language):
        # The langdetect library does not provide a direct confidence score.
        # This is a simplified way to simulate confidence.
        try:
            probs = detect_langs(text)
            for lang_prob in probs:
                if lang_prob.lang == language:
                    return lang_prob.prob
            return 0
        except LangDetectException:
            return 0

