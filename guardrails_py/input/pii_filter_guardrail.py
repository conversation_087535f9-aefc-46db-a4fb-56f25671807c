
from presidio_analyzer import AnalyzerEngine
from presidio_anonymizer import AnonymizerEngine
from presidio_anonymizer.entities import OperatorConfig
from presidio_analyzer.nlp_engine import NlpEngine

class PIIFilterGuardrail:
    def __init__(self, config={}):
        self.config = {
            "anonymization_strategy": "anonymize",  # or "block"
            "anonymizers": {
                "DEFAULT": OperatorConfig("replace", {"new_value": "[REDACTED]"}),
                "PHONE_NUMBER": OperatorConfig("mask", {"type": "mask", "masking_char": "*", "chars_to_mask": 12, "from_end": True}),
                "EMAIL_ADDRESS": OperatorConfig("replace", {"new_value": "[EMAIL_REDACTED]"}),
            },
            **config
        }

        # Setup Presidio components for Italian
        # Explicitly load the Italian spaCy model
        nlp_engine = NlpEngine(models=[{"lang_code": "it", "model_name": "it_core_news_sm"}])
        self.analyzer = AnalyzerEngine(nlp_engine=nlp_engine)
        self.anonymizer = AnonymizerEngine()

    def process(self, text, context={}):
        analyzer_results = self.analyzer.analyze(text=text, language='it')

        if not analyzer_results:
            return {"action": "ALLOW"}

        if self.config["anonymization_strategy"] == "block":
            return {
                "action": "BLOCK",
                "reason": "PII detected",
                "details": {"pii_found": [res.to_dict() for res in analyzer_results]}
            }

        # Default to anonymize
        anonymized_result = self.anonymizer.anonymize(
            text=text,
            analyzer_results=analyzer_results,
            operators=self.config["anonymizers"]
        )

        return {
            "action": "MODIFY",
            "modified_text": anonymized_result.text,
            "warning": "PII was detected and anonymized.",
            "details": {"pii_found": [res.to_dict() for res in analyzer_results]}
        }

