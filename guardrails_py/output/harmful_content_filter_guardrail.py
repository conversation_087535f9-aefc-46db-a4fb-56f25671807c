
import re
import random

class HarmfulContentFilterGuardrail:
    def __init__(self, config={}):
        self.config = {
            "confidence_threshold": 0.7,
            "enable_replacement": True,
            "categories": {
                "violence": True,
                "hate_speech": True,
                "self_harm": True,
            },
            **config
        }
        self.initialize_patterns()
        self.initialize_replacements()

    def initialize_patterns(self):
        self.patterns = {
            "violence": [
                r"\b(kill|murder|assassinate|execute|slaughter)\s+(you|him|her|them|someone)\b",
                r"\b(hurt|harm|injure|attack|assault|beat)\s+(you|someone|people)\b",
            ],
            "hate_speech": [
                r"\b(hate|despise|loathe)\s+(all|every|those)\s+(people|persons|individuals)\b",
            ],
            "self_harm": [
                r"\b(kill|hurt|harm)\s+yourself\b",
                r"\b(suicide|self-harm|self-injury)\s+(is|would\s+be|might\s+be)\s+(good|better|solution)\b",
            ],
        }

    def initialize_replacements(self):
        self.replacements = {
            "violence": ["I cannot provide information that could cause physical harm."],
            "hate_speech": ["I do not support hate speech or discrimination."],
            "self_harm": ["If you are going through a difficult time, I recommend talking to a qualified professional."],
            "default": ["This content is not appropriate for our conversation."]
        }

    def process(self, text, context={}):
        analysis_result = self._analyze_content(text)

        if analysis_result["is_harmful"] and analysis_result["confidence"] >= self.config["confidence_threshold"]:
            replacement_text = self._generate_safe_replacement(analysis_result["category"])
            return {
                "action": "MODIFY",
                "modified_text": replacement_text,
                "warning": "The content has been modified to ensure safety.",
                "details": analysis_result
            }

        return {"action": "ALLOW"}

    def _analyze_content(self, text):
        max_confidence = 0
        primary_category = None

        for category, patterns in self.patterns.items():
            if not self.config["categories"].get(category):
                continue
            for pattern in patterns:
                if re.search(pattern, text, re.IGNORECASE):
                    confidence = self._calculate_harm_confidence(category)
                    if confidence > max_confidence:
                        max_confidence = confidence
                        primary_category = category
        
        return {
            "is_harmful": max_confidence > 0,
            "confidence": max_confidence,
            "category": primary_category
        }

    def _calculate_harm_confidence(self, category):
        if category in ["violence", "self_harm", "hate_speech"]:
            return 0.9
        return 0.7

    def _generate_safe_replacement(self, category):
        options = self.replacements.get(category, self.replacements["default"])
        return random.choice(options)

