/* Stili generali */
body {
    font-family: 'Roboto', sans-serif;
    margin: 0;
    padding: 0;
}

/* Widget del chatbot */
#chatbot-widget {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
}

/* Pulsante di avvio */
#chatbot-launcher {
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    cursor: pointer;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    display: flex;
    justify-content: center;
    align-items: center;
    transition: background-color 0.3s;
}

#chatbot-launcher:hover {
    background-color: #0056b3;
}

/* Finestra del chatbot */
#chatbot-window {
    width: 350px;
    height: 500px;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transition: transform 0.3s ease-out, opacity 0.3s ease-out;
    transform-origin: bottom right;
}

#chatbot-window.hidden {
    transform: scale(0);
    opacity: 0;
    display: none;
}

/* Header del chat */
.chat-header {
    background-color: #007bff;
    color: white;
    padding: 10px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chat-header h2 {
    margin: 0;
    font-size: 1.1em;
}

#close-chatbot {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
}

/* Box della chat */
.chat-box {
    flex-grow: 1;
    padding: 15px;
    overflow-y: auto;
    background-color: #f9f9f9;
}

/* Area di input */
.chat-input-area {
    padding: 10px;
    border-top: 1px solid #ddd;
}

.chat-form {
    display: flex;
}

#user-input {
    flex-grow: 1;
    border: 1px solid #ccc;
    border-radius: 20px;
    padding: 10px 15px;
    font-size: 1em;
}

#send-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0 10px;
    color: #007bff;
}

/* Stili dei messaggi */
.message {
    margin-bottom: 15px;
    padding: 10px 15px;
    border-radius: 18px;
    max-width: 80%;
    line-height: 1.4;
}

.user-message {
    background-color: #007bff;
    color: white;
    align-self: flex-end;
    border-bottom-right-radius: 4px;
}

.bot-message {
    background-color: #e9ecef;
    color: #333;
    align-self: flex-start;
    border-bottom-left-radius: 4px;
}

/* Modal */
.modal {
    display: none; 
    position: fixed; 
    z-index: 1001; 
    left: 0;
    top: 0;
    width: 100%; 
    height: 100%; 
    overflow: auto; 
    background-color: rgba(0,0,0,0.4);
}

.modal-content {
    background-color: #fefefe;
    margin: 15% auto; 
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
    max-width: 400px;
    border-radius: 10px;
    text-align: center;
}
