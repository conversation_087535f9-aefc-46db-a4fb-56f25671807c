:root {
    --background-color: #f0f2f5;
    --container-bg: #ffffff;
    --header-bg: #f5f5f5;
    --bot-msg-bg: #e9e9eb;
    --user-msg-bg: #0084ff;
    --user-msg-text: #ffffff;
    --text-color: #0d0d0d;
    --border-color: #e0e0e0;
    --font-family: 'Roboto', sans-serif;
}

body {
    font-family: var(--font-family);
    background-color: var(--background-color);
    margin: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

.chat-container {
    width: 90%;
    max-width: 700px;
    height: 90vh;
    max-height: 800px;
    background-color: var(--container-bg);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.chat-header {
    background-color: var(--header-bg);
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    text-align: center;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.menu-container {
    position: relative;
}

.menu-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.menu-btn:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

.dropdown-menu {
    display: none;
    position: absolute;
    right: 0;
    top: 40px;
    background-color: var(--container-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: 100;
    width: 150px;
    overflow: hidden;
}

.dropdown-menu.show {
    display: block;
}

.dropdown-menu a {
    display: block;
    padding: 12px 16px;
    color: var(--text-color);
    text-decoration: none;
    font-size: 0.9rem;
}

.dropdown-menu a:hover {
    background-color: var(--background-color);
}


.chat-header h2 {
    margin: 0;
    font-size: 1.2rem;
    color: var(--text-color);
}

.chat-box {
    flex-grow: 1;
    padding: 1rem;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.message {
    padding: 0.6rem 1rem;
    border-radius: 18px;
    max-width: 80%;
    line-height: 1.4;
    word-wrap: break-word;
}

.message.bot {
    background-color: var(--bot-msg-bg);
    color: var(--text-color);
    align-self: flex-start;
    border-bottom-left-radius: 4px;
}

.message.user {
    background-color: var(--user-msg-bg);
    color: var(--user-msg-text);
    align-self: flex-end;
    border-bottom-right-radius: 4px;
}

/* Stili per il contenuto HTML generato dal bot */
.message.bot p {
    margin: 0;
}
.message.bot a {
    color: #005cbf;
    text-decoration: none;
    font-weight: 500;
}
.message.bot a:hover {
    text-decoration: underline;
}

.chat-input-area {
    padding: 1rem;
    border-top: 1px solid var(--border-color);
}

.chat-form {
    display: flex;
    gap: 0.5rem;
}

#user-input {
    flex-grow: 1;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 18px;
    font-size: 1rem;
}
#user-input:disabled {
    background-color: #f5f5f5;
    cursor: not-allowed;
}

#send-btn {
    background-color: var(--user-msg-bg);
    color: white;
    border: none;
    border-radius: 50%;
    width: 48px;
    height: 48px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: background-color 0.2s;
}
#send-btn:hover:not(:disabled) {
    background-color: #0073e6;
}
#send-btn:disabled {
    background-color: #a0cffc;
    cursor: not-allowed;
}


/* Modal Styles */
.modal {
    position: fixed;
    z-index: 10;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0,0,0,0.5);
    display: flex;
    justify-content: center;
    align-items: center;
}
.modal-content {
    background-color: #fff;
    padding: 2rem;
    border-radius: 8px;
    width: 90%;
    max-width: 400px;
    text-align: center;
}
.modal-content input {
    width: calc(100% - 2rem);
    padding: 0.75rem;
    margin-top: 1rem;
    margin-bottom: 1rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
}
.modal-content button {
    background-color: var(--user-msg-bg);
    color: white;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    width: 100%;
}
.modal-status {
    margin-top: 1rem;
    font-weight: 500;
}
.modal-status.success { color: #28a745; }
.modal-status.error { color: #dc3545; }